#!/usr/bin/env python3
"""
EP2002优化解析器性能测试
"""
import os
import sys
import time
import binfile_parser

def test_performance(bin_file):
    if not os.path.exists(bin_file):
        print(f"测试文件 {bin_file} 不存在")
        return False
    
    file_size = os.path.getsize(bin_file)
    print(f"测试文件大小: {file_size / 1024 / 1024:.2f} MB")
    
    # 示例信号配置
    signals = [
        {
            "name": "Physical Car Code",
            "message_type": "Status", 
            "node_types": ["Car"],
            "node_count": 1,
            "bits_per_item": 8,
            "storage_period": 2000,
            "byte_offset": 16,
            "bit_offset": 0,
            "scaling": 1.0,
            "unit": "",
            "element_count": 1
        }
    ]
    
    print("开始优化解析测试...")
    start_time = time.time()
    
    try:
        result = binfile_parser.parse_bin_file(bin_file, signals, 1)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"解析完成!")
        print(f"耗时: {duration:.2f} 秒")
        print(f"处理速度: {file_size / 1024 / 1024 / duration:.2f} MB/s")
        print(f"记录数量: {len(result.get('records', []))}")
        print(f"信号数量: {len(result.get('signal_data', {}))}")
        
        # 检查调试文件
        if os.path.exists('os_c_test.txt'):
            with open('os_c_test.txt', 'r') as f:
                debug_content = f.read()
                print("✓ 调试信息已记录")
        
        return True
        
    except Exception as e:
        print(f"解析失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_performance(sys.argv[1])
    else:
        print("用法: python3 performance_test.py <bin_file>")
