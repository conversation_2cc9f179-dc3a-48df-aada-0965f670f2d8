data: {"status": "uploading", "progress": 5, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 5, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 5, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 10, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 6, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 10, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 15, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 7, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 15, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 20, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 8, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 20, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 25, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 9, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 25, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 30, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 10, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 30, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 35, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 11, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 35, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 40, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 12, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 40, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 45, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 13, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 45, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 50, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 14, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 50, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 55, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 15, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 55, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 60, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 16, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 60, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 65, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 17, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 65, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 70, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 18, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 70, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 75, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 19, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 75, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 80, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 20, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 80, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 85, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 21, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 85, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 90, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 22, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 90, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 95, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 23, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 95, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 99, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 24, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 99, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 99, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 25, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 99, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 99, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 26, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 99, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 99, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 27, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 99, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 99, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 28, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 99, "progress_type": "simulated"}

data: {"status": "uploading", "progress": 99, "uploaded_files": 0, "total_files": 3, "message": "\u5df2\u4e0a\u4f20 0/3 \u4e2a\u6587\u4ef6 (simulated)", "wait_time": 29, "upload_dir_exists": false, "real_progress": 0, "simulated_progress": 99, "progress_type": "simulated"}

data: {"status": "error", "message": "\u4e0a\u4f20\u76ee\u5f55\u4e0d\u5b58\u5728\uff0c\u7b49\u5f85\u65f6\u95f4\u5df2\u8d85\u8fc730\u79d2: /Users/<USER>/Desktop/datapiPeline/metisgrid/backend/data_pipeline/uploadFiles/test5_20250809_165938"}

event: close
data: close

