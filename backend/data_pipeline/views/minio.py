import os
import json
import shutil
import asyncio
import logging
from datetime import timedelta, datetime
from asyncio import Queue
from rest_framework.viewsets import ViewSet
from minio import Minio
from minio.commonconfig import ENABLED
from minio.versioningconfig import VersioningConfig
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.http import HttpResponse, StreamingHttpResponse
from rest_framework.decorators import action
from data_pipeline.models import DataPipelineFile

logger = logging.getLogger(__name__)
# 创建一个全局队列来存储进度条进度更新
progress_queue = Queue()

async def sse_event_stream(queue, param):
    sseMinio = MinioStorageView()
    project_name = param['project_name']
    project_type = param['project_type']
    pgsql_user_id = param['pgsql_user_id']
    pgsql_user_name = param['pgsql_user_name']
    project_id = param['project_id']
    try:
        # 清空队列中的旧数据
        while not queue.empty():
            try:
                queue.get_nowait()
            except Exception:
                pass

        # 获取文件路径
        item_path = sseMinio.post(param)  # post方法会重置标志
        
        # 启动一个单独的任务来执行上传
        upload_task = asyncio.create_task(
            asyncio.to_thread(
                sseMinio.upload_directory,
                item_path,
                project_name,
                project_type,
                pgsql_user_id,
                pgsql_user_name,
                project_id
            )
        )
        
        # 等待进度更新直到上传完成
        try:
            while True:  # 修改循环条件
                try:
                    data = await asyncio.wait_for(queue.get(), timeout=30.0)  # 添加超时
                    yield f"data: {json.dumps(data)}\n\n"
                    
                    # 只有在确实完成时才发送完成消息
                    if upload_task.done() and data['progress'] >= 100:
                        data['status'] = 'Completed'
                        yield f"data: {json.dumps(data)}\n\n"
                        yield "event: close\ndata: close\n\n"
                        break
                except asyncio.TimeoutError:
                    # 检查任务是否完成
                    if upload_task.done():
                        break
                    continue
                
        except asyncio.CancelledError:
            print("SSE connection cancelled")
            sseMinio.stop_upload()
            yield f"data: {json.dumps({'status': 'cancelled'})}\n\n"
            yield "event: close\ndata: close\n\n"
            raise
        finally:
            if not upload_task.done():
                sseMinio.stop_upload()
                upload_task.cancel()
                
    except Exception as e:
        print(f"SSE stream error: {e}")
        sseMinio.stop_upload()
        yield f"data: {json.dumps({'status': 'error', 'message': str(e)})}\n\n"
        yield "event: close\ndata: close\n\n"  # 发送关闭事件
        raise

async def sse_progress_view_minio(request):
    param_str = request.GET.get('param', '')
    try:
        param = json.loads(param_str)

    except json.JSONDecodeError:
        logger.error("JSON解析错误")
        return StreamingHttpResponse(status=400)
    except KeyError:
        logger.error("缺少必要的参数")
        return StreamingHttpResponse(status=400)

    response = StreamingHttpResponse(
        sse_event_stream(progress_queue, param),
        content_type='text/event-stream'
    )
    response['Cache-Control'] = 'no-cache'
    # response['Connection'] = 'keep-alive'
    return response


class MinioStorageView(ViewSet):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.minio_client = Minio(
            settings.MINIO_CONFIG["endpoint"],
            access_key=settings.MINIO_CONFIG["access_key"],
            secret_key=settings.MINIO_CONFIG["secret_key"],
            secure=False
        )
        self.bucket_name = settings.MINIO_CONFIG["bucket"]
        self._stop_upload = False
        self._progress_sent = False
        self._init_bucket()

    def _init_bucket(self):
        """初始化bucket并启用版本控制"""
        try:
            if not self.minio_client.bucket_exists(self.bucket_name):
                self.minio_client.make_bucket(self.bucket_name)
            self.minio_client.set_bucket_versioning(
                self.bucket_name,
                VersioningConfig(status=ENABLED)
            )
        except Exception as e:
            print(f"初始化 MinIO 出错: {str(e)}")

    def _format_time_with_timezone(self, time_obj):
        """格式化时间并加8小时时区转换"""
        if not time_obj:
            return ''
        time_plus_8 = time_obj + timedelta(hours=8)
        return time_plus_8.strftime('%Y-%m-%d %H:%M:%S')

    def _get_database_file_info(self, version_id, DataPipelineFile, pgsql_user_id, pgsql_user_name, project_id):
        """根据version_id获取 postgresql数据库中的文件信息"""
        try:
            obj_postgresql = DataPipelineFile.objects.get(
                file_Minio_version_id=version_id,
                pgsql_user_id=pgsql_user_id,
                pgsql_user_name=pgsql_user_name,
                project_id=project_id
            )
            return {
                'start_time': self._format_time_with_timezone(obj_postgresql.start_time),
                'end_time': self._format_time_with_timezone(obj_postgresql.end_time),
                'file_Minio_version_id': obj_postgresql.file_Minio_version_id[:5] if obj_postgresql.file_Minio_version_id else '',
                'file_Postgresql_id': obj_postgresql.id if obj_postgresql.id else '',
                'influx_ubique_tagId': obj_postgresql.influx_ubique_tagId if obj_postgresql.id else '',
                'pgsql_user_name': obj_postgresql.pgsql_user_name or pgsql_user_name,
                'pgsql_user_id': obj_postgresql.pgsql_user_id or pgsql_user_id,
                'project_id': obj_postgresql.project_id or project_id
            }
        except DataPipelineFile.DoesNotExist:
            return {
                'start_time': '',
                'end_time': '',
                'file_Minio_version_id': '',
                'file_Postgresql_id': '',
                'influx_ubique_tagId': '',
                'pgsql_user_name': pgsql_user_name,
                'pgsql_user_id': pgsql_user_id,
                'project_id': project_id
            }

    def upload_directory(self, local_path, project_name, project_type, pgsql_user_id, pgsql_user_name, project_id):
        """上传目录或文件到MinIO"""
        try:
            uploaded_items = []
            binFiles_Minio_info = []
            iteration = 0
            
            if os.path.isdir(local_path):
                # 处理文件夹
                folder_name = os.path.basename(local_path).rstrip('/')
                minio_path = f"{pgsql_user_name}_{pgsql_user_id}/{project_type}/{project_name}/{folder_name}"
                
                # 首先计算总文件数
                total_files = sum([len(files) for _, _, files in os.walk(local_path)])
                
                for root, _, files in os.walk(local_path):
                    for file in files:
                        # 检查是否应该停止
                        if self._stop_upload:
                            print('上传任务被取消')
                            return uploaded_items
                            
                        local_file_path = os.path.join(root, file)
                        relative_path = os.path.relpath(local_file_path, local_path)
                        object_name = os.path.join(minio_path, relative_path).replace("\\", "/")
                        
                        try:
                            result = self._upload_single_file(local_file_path, object_name, pgsql_user_id, pgsql_user_name, project_id)
                            if result:
                                binFiles_Minio_info.append(result)
                                iteration += 1
                                self._update_progress(iteration, total_files, binFiles_Minio_info)
                        except Exception as e:
                            print(f"上传文件 {object_name} 失败: {str(e)}")
                            continue
                
                uploaded_items.append(os.path.basename(local_path))
            else:
                # 检查是否应该停止
                if self._stop_upload:
                    print('上传任务被取消')
                    return uploaded_items
                    
                # 处理单个文件
                file_name = os.path.basename(local_path)
                object_name = f"{pgsql_user_name}_{pgsql_user_id}/{project_type}/{project_name}/noFolderFileList/{file_name}"
                
                try:
                    result = self._upload_single_file(local_path, object_name, pgsql_user_id, pgsql_user_name, project_id)
                    if result:
                        binFiles_Minio_info.append(result)
                        print('binFiles_Minio_info =====', binFiles_Minio_info)
                        self._update_progress(1, 1, binFiles_Minio_info)
                        uploaded_items.append(file_name)
                except Exception as e:
                    print(f"上传文件 {file_name} 失败: {str(e)}")
            
            return uploaded_items
        except Exception as e:
            print(f"上传过程出错: {str(e)}")
            return []

    def _upload_single_file(self, local_file_path, object_name, pgsql_user_id, pgsql_user_name, project_id):
        """上传单个文件到MinIO"""
        try:
            self.minio_client.stat_object(self.bucket_name, object_name)
        except Exception:
            print(f"文件 {object_name} 不存在，将创建首个版本")

        result = self.minio_client.fput_object(
            self.bucket_name,
            object_name,
            local_file_path
        )
        
        parts = result.object_name.split('/')
        fileFolder_name = parts[-2]  # 'test5_20250808_131907' 或者 'noFolderFileList'
        fileFolder_name = fileFolder_name if fileFolder_name != 'noFolderFileList' else None
        file_name = parts[-1]    # 'LOG00760_副本4.BIN'

        # 根据当前文件的minio信息 (project_id + pgsql_user_id + fileFolder_name+ file_name + version_id !== null), 查询并筛选出 pgsql 数据库里对应的id, 再把这个id数据的file_Minio_version_id 更新为当前文件的result.version_id
        # 查询 pgsql 数据库里对应的id
        updated = DataPipelineFile.objects.filter(
            project_id=project_id,
            pgsql_user_id=pgsql_user_id,
            fileFolder_name=fileFolder_name,
            file_name=file_name,
            file_Minio_version_id=None
        ).update(file_Minio_version_id=result.version_id)

        if not updated:
            print("No pgsql-record matched for minio-file update.")
        
        return {
            "object_name": result.object_name,
            "version_id": result.version_id,
            "pgsql_user_id": pgsql_user_id,
            "pgsql_user_name": pgsql_user_name,
            "project_id": project_id,
            "fileFolder_name": fileFolder_name,
            "file_name": file_name
        }

    def _update_progress(self, current, total, binFiles_Minio_info):
        """更新进度到队列"""
        # 确保是整数
        current = int(current)
        total = int(total)

        is_completed = current >= total
        progress = 100 if is_completed else int((current / total) * 100)

        progress_data = {
            'progress': progress,
            'current': current,
            'total': total,
            'binFiles_Minio_info': binFiles_Minio_info,
            'status': "Completed" if is_completed else "Processing"
        }
        progress_queue.put_nowait(progress_data)

    def _prepare_download_directory(self):
        """准备下载目录"""
        download_dir = os.path.join(settings.BASE_DIR, 'downloadFiles')
        if not os.path.exists(download_dir):
            os.makedirs(download_dir)
        else:
            # 清空目录
            for file in os.listdir(download_dir):
                file_path = os.path.join(download_dir, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)
        return download_dir

    def stop_upload(self):
        """设置停止标志"""
        self._stop_upload = True

    def _validate_project_params(self, param):
        """验证项目参数"""
        project_name = param['project_name']
        project_type = param['project_type']
        project_id = param['project_id']
        if not project_name:
            return None, Response({"error": "缺少项目名称"}, status=status.HTTP_400_BAD_REQUEST)
        if not project_type:
            return None, Response({"error": "缺少项目类型"}, status=status.HTTP_400_BAD_REQUEST)
        if not param['pgsql_user_id']:
            return None, Response({"error": "缺少用户ID"}, status=status.HTTP_400_BAD_REQUEST)
        if not param['pgsql_user_name']:
            return None, Response({"error": "缺少用户名"}, status=status.HTTP_400_BAD_REQUEST)
        if not project_id:
            return None, Response({"error": "缺少项目ID"}, status=status.HTTP_400_BAD_REQUEST)

        return (project_name, project_type, param['pgsql_user_id'], param['pgsql_user_name'], project_id), None

    def post(self, param):
        """处理文件上传minio请求"""
        try:
            # 验证参数
            validation_result, error_response = self._validate_project_params(param)
            if error_response:
                return error_response
            project_name, project_type, pgsql_user_id, pgsql_user_name, project_id = validation_result

            upload_dir = os.path.join(settings.UPLOAD_FILES_DIR)
            if not os.path.exists(upload_dir):
                return Response({"error": "上传目录不存在"}, status=status.HTTP_404_NOT_FOUND)

            items = [d for d in os.listdir(upload_dir)]
            if not items:
                return Response({"error": "没有找到要上传的文件或文件夹"}, 
                              status=status.HTTP_404_NOT_FOUND)

            # 默认对 UPLOAD_FILES_DIR/ 路径下的第一个文件夹进行上传 upload_directory()
            item = items[0]
            item_path = os.path.join(upload_dir, item)

            return item_path

        except Exception as e:
            return Response({"error": str(e)}, 
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_file_history(self, request):
        """获取文件上传历史记录，包含版本信息"""
        try:
            # 验证参数
            param = {
                'project_name': request['project_name'],
                'project_type': request['project_type'],
                'pgsql_user_id': request['pgsql_user_id'],
                'pgsql_user_name': request['pgsql_user_name'],
                'project_id': request['project_id']
            }
            validation_result, error_response = self._validate_project_params(param)
            if error_response:
                # 转换错误响应格式
                error_data = error_response.data
                return Response({
                    "code": 4000,
                    "message": error_data.get("error", "参数错误")
                }, status=status.HTTP_400_BAD_REQUEST)
            project_name, project_type, pgsql_user_id, pgsql_user_name, project_id = validation_result

            # 根据 pgsql_user_id + project_id 查询数据库中的所有文件记录
            file_records = DataPipelineFile.objects.filter(
                pgsql_user_id=pgsql_user_id,
                project_id=project_id
            ).order_by('-update_datetime')

            # 分组处理文件记录
            grouped_files = {}  # 存储单个文件
            folder_files = {}   # 存储文件夹

            for record in file_records:
                # 格式化时间
                start_time = self._format_time_with_timezone(record.start_time)
                end_time = self._format_time_with_timezone(record.end_time)
                file_Minio_version_id = record.file_Minio_version_id[:5] if record.file_Minio_version_id else ''

                if record.fileFolder_name:
                    # 情况1: 含有 fileFolder_name 数据的item，视为文件夹下的文件
                    folder_name = record.fileFolder_name
                    folder_key = f"{folder_name}/"

                    # 初始化文件夹记录
                    if folder_key not in folder_files:
                        # 构建文件夹的 MinIO 路径
                        folder_path = f"{pgsql_user_name}_{pgsql_user_id}/{project_type}/{project_name}/{folder_name}/"

                        folder_files[folder_key] = {
                            'name': folder_name,
                            'path': folder_path,
                            'size': 0,
                            'last_modified': record.update_datetime,
                            'files': [],
                            'start_time': self._format_time_with_timezone(record.fileFolder_startTime),
                            'end_time': self._format_time_with_timezone(record.fileFolder_endTime),
                            'influx_ubique_tagId': record.influx_ubique_tagId or '',
                            'pgsql_user_name': record.pgsql_user_name or pgsql_user_name,
                            'pgsql_user_id': record.pgsql_user_id or pgsql_user_id,
                            'binFile_id': record.id,
                            'project_id': record.project_id
                        }

                    # 更新文件夹信息
                    folder_files[folder_key]['size'] += record.file_size or 0
                    if record.update_datetime > folder_files[folder_key]['last_modified']:
                        folder_files[folder_key]['last_modified'] = record.update_datetime

                    # 构建文件的完整 MinIO 路径并添加到文件列表
                    file_minio_path = f"pgsql_id {record.id} : {pgsql_user_name}_{pgsql_user_id}/{project_type}/{project_name}/{folder_name}/{record.file_name}"
                    folder_files[folder_key]['files'].append(file_minio_path)

                else:
                    # 情况2: 没有 fileFolder_name 数据的item，视为单独的文件
                    file_name = record.file_name
                    # 构建单个文件的 MinIO 路径
                    file_minio_path = f"pgsql_id {record.id} : {pgsql_user_name}_{pgsql_user_id}/{project_type}/{project_name}/noFolderFileList/{file_name}"

                    if file_minio_path not in grouped_files:
                        grouped_files[file_minio_path] = {
                            'name': file_name,
                            'size': record.file_size or 0,
                            'last_modified': record.update_datetime,
                            'path': file_minio_path,
                            'versions': [],
                            'start_time': start_time,
                            'end_time': end_time,
                            'file_Minio_version_id': file_Minio_version_id,
                            'file_Postgresql_id': record.id,
                            'influx_ubique_tagId': record.influx_ubique_tagId or '',
                            'pgsql_user_name': record.pgsql_user_name or pgsql_user_name,
                            'pgsql_user_id': record.pgsql_user_id or pgsql_user_id,
                            'binFile_id': record.id,
                            'project_id': record.project_id
                        }

                    # 添加版本信息
                    grouped_files[file_minio_path]['versions'].append({
                        'version_id': record.file_Minio_version_id,
                        'last_modified': record.update_datetime,
                        'is_latest': True,  # 可以根据需要调整逻辑
                        'start_time': start_time,
                        'end_time': end_time,
                        'file_Minio_version_id': file_Minio_version_id,
                        'file_Postgresql_id': record.id,
                        'influx_ubique_tagId': record.influx_ubique_tagId or '',
                        'pgsql_user_name': record.pgsql_user_name or pgsql_user_name,
                        'pgsql_user_id': record.pgsql_user_id or pgsql_user_id,
                        'binFile_id': record.id,
                        'project_id': record.project_id
                    })

            # 将文件夹信息和单个文件信息合并到最终结果中
            final_history = list(grouped_files.values()) + list(folder_files.values())

            # 按最后修改时间排序
            final_history.sort(key=lambda x: x['last_modified'], reverse=True)

            return Response({
                "code": 2000,
                "data": final_history,
                "message": f"成功查询到{len(final_history)}条历史记录" if len(final_history) > 0 else "没有找到对应的记录"
            })
        except Exception as e:
            print(f"获取历史记录失败: {str(e)}")
            return Response({
                "code": 5000,
                "message": f"获取历史记录失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['POST'], detail=False)
    def get_file_upload_history(self, request):
        """处理查询文件上传历史请求"""
        if request.data['action'] == 'history':
            return self.get_file_history(request.data)

    def get_folder_files(self, folder_name):
        """下载指定文件夹中的所有文件"""
        try:
            objects = self.minio_client.list_objects(
                self.bucket_name,
                prefix=f"{folder_name}/",
                recursive=True
            )

            download_dir = os.path.join(settings.BASE_DIR, 'downloadFiles', folder_name)
            os.makedirs(download_dir, exist_ok=True)

            for obj in objects:
                local_path = os.path.join(download_dir, os.path.basename(obj.object_name))
                self.minio_client.fget_object(
                    self.bucket_name,
                    obj.object_name,
                    local_path
                )
        except Exception as e:
            print(f"下载文件夹出错: {str(e)}")
            raise e

    def get_single_files(self, file_list, version_id=None):
        """下载指定的单个文件（支持版本）"""
        try:
            download_dir = os.path.join(settings.BASE_DIR, 'downloadFiles')
            os.makedirs(download_dir, exist_ok=True)

            for file_name in file_list:
                local_path = os.path.join(download_dir, os.path.basename(file_name))
                
                # 获取文件的所有版本信息
                versions = list(self.minio_client.list_objects(
                    self.bucket_name,
                    prefix=file_name,
                    include_version=True
                ))
                
                if version_id:
                    # 下载指定版本
                    self.minio_client.fget_object(
                        self.bucket_name,
                        file_name,
                        local_path,
                        version_id=version_id
                    )
                else:
                    # 下载最新版本
                    latest_version = next(
                        (v for v in versions if v.is_latest),
                        None
                    )
                    if latest_version:
                        self.minio_client.fget_object(
                            self.bucket_name,
                            file_name,
                            local_path,
                            version_id=latest_version.version_id
                        )
                    else:
                        # 如果没有找到最新版本，使用默认下载
                        self.minio_client.fget_object(
                            self.bucket_name,
                            file_name,
                            local_path
                        )
        except Exception as e:
            print(f"下载文件出错: {str(e)}")
            raise e