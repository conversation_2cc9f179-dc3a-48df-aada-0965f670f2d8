from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.json_response import ErrorResponse, SuccessResponse
from data_pipeline.models import DataPipelineFile
from dvadmin.utils.filters import CoreModelFilterBankend
from rest_framework.permissions import IsAuthenticated
import os
from rest_framework.decorators import action
from django.conf import settings
from datetime import datetime
import shutil

from rest_framework import status
from rest_framework.response import Response
from django.utils.dateparse import parse_datetime
from datetime import datetime
import json
import time
from django.http import StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import threading
import glob
import asyncio

# ================================================= #
# ************** SSE 相关函数 ************** #
# ================================================= #

async def sse_progress_view_file_upload(request):
    """
    文件上传进度 SSE 视图 - 在连接建立时触发文件保存
    """
    param_str = request.GET.get('param', '')
    try:
        param = json.loads(param_str)
        total_files = param.get('total_files', 0)
        is_folder = param.get('is_folder', False)
        folder_name = param.get('folder_name', '')
        project_type = param.get('project_type', '')
        files_info = param.get('files_info', [])
        pgsql_user_id = param.get('pgsql_user_id', '')
        pgsql_user_name = param.get('pgsql_user_name', '')

        if not total_files or not files_info:
            return StreamingHttpResponse(status=400)

    except json.JSONDecodeError:
        return StreamingHttpResponse(status=400)
    except KeyError:
        return StreamingHttpResponse(status=400)

    # 确定上传目录（不清空，因为文件已经通过 upload_file() 接口上传了）
    base_upload_dir = os.path.join(settings.BASE_DIR, 'data_pipeline', 'uploadFiles')

    # 如果是文件夹上传，需要找到对应的文件夹目录
    if is_folder and folder_name:
        # 查找最新的文件夹（因为文件夹名包含时间戳）
        pattern = os.path.join(base_upload_dir, f"{folder_name}_*")
        matching_dirs = glob.glob(pattern)
        if matching_dirs:
            # 选择最新的目录
            upload_dir = max(matching_dirs, key=os.path.getctime)
        else:
            # 如果没找到，创建一个新的
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            folder_path = f"{folder_name}_{timestamp}"
            upload_dir = os.path.join(base_upload_dir, folder_path)
            # os.makedirs(upload_dir, exist_ok=True)
    else:
        upload_dir = os.path.join(base_upload_dir, 'noFolderFileList')
        # os.makedirs(upload_dir, exist_ok=True)

    async def sse_generator():
        uploaded_count = 0
        max_wait_time = 60  # 最大等待时间60秒
        wait_time = 0
        uploaded_files_info = []

        while uploaded_count < total_files and wait_time < max_wait_time:
            try:
                # 每隔1秒检查一次上传目录中的文件数量
                await asyncio.sleep(1)
                wait_time += 1

                # 统计目录中的文件数量
                if os.path.exists(upload_dir):
                    # 递归统计所有文件（包括子目录中的文件）
                    file_pattern = os.path.join(upload_dir, '**', '*')
                    all_files = glob.glob(file_pattern, recursive=True)
                    # 只统计文件，不包括目录
                    uploaded_count = len([f for f in all_files if os.path.isfile(f)])
                else:
                    uploaded_count = 0

                # 计算进度百分比
                progress = min(int((uploaded_count / total_files) * 100), 100)

                # 构造进度数据
                progress_data = {
                    'status': 'uploading',
                    'progress': progress,
                    'uploaded_files': uploaded_count,
                    'total_files': total_files,
                    'message': f'已上传 {uploaded_count}/{total_files} 个文件'
                }

                # 发送进度数据
                yield f"data: {json.dumps(progress_data)}\n\n"

                # 如果上传完成，发送完成消息并退出
                if uploaded_count >= total_files:
                    # 构建已上传文件的信息列表
                    if os.path.exists(upload_dir):
                        file_pattern = os.path.join(upload_dir, '**', '*')
                        all_files = glob.glob(file_pattern, recursive=True)
                        uploaded_files_info = []
                        for file_path in all_files:
                            if os.path.isfile(file_path):
                                file_name = os.path.basename(file_path)
                                file_size = os.path.getsize(file_path)
                                uploaded_files_info.append({
                                    'name': file_name,
                                    'size': file_size,
                                    'path': file_path,
                                    "pgsql_user_id": pgsql_user_id,
                                    "pgsql_user_name": pgsql_user_name
                                })

                    progress_data['status'] = 'completed'
                    progress_data['message'] = '文件上传完成'
                    progress_data['files'] = uploaded_files_info
                    yield f"data: {json.dumps(progress_data)}\n\n"
                    yield "event: close\ndata: close\n\n"
                    break

            except Exception as e:
                error_data = {
                    'status': 'error',
                    'message': f'监控进度时出错: {str(e)}'
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                yield "event: close\ndata: close\n\n"
                break

        # 如果超时，发送超时消息
        if wait_time >= max_wait_time and uploaded_count < total_files:
            timeout_data = {
                'status': 'timeout',
                'message': '上传监控超时'
            }
            yield f"data: {json.dumps(timeout_data)}\n\n"
            yield "event: close\ndata: close\n\n"

    response = StreamingHttpResponse(
        sse_generator(),
        content_type='text/event-stream'
    )
    response['Cache-Control'] = 'no-cache'
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Headers'] = 'Cache-Control'
    return response

# ================================================= #
# ************** 序列化器 ************** #
# ================================================= #

class DataPipelineFileSerializer(CustomModelSerializer):
    """
    数据管道文件-序列化器
    """
    class Meta:
        model = DataPipelineFile
        fields = "__all__"
        read_only_fields = ["id"]


# ================================================= #
# ************** 视图集 ************** #
# ================================================= #

class DataPipelineFileModelViewSet(CustomModelViewSet):
    """
    数据管道文件管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    upload_file:上传文件
    """
    queryset = DataPipelineFile.objects.all()
    serializer_class = DataPipelineFileSerializer
    permission_classes = [IsAuthenticated]  # 添加认证要求
    # 移除数据权限过滤器，只保留核心模型过滤器
    extra_filter_class = [CoreModelFilterBankend]
    filter_fields = ['project_id', 'file_name', 'file_status', 'version']
    search_fields = ['project_id', 'file_name']

    @action(methods=['POST'], detail=False)
    def upload_file(self, request):
        """
        文件上传处理 - 同步上传，返回完整 uploaded_files || 采用了for循环同步方式执行 upload_files_background()
        """
        try:
            files = request.FILES.getlist('files')
            is_folder = request.POST.get('isFolder', 'false').lower() == 'true'
            folder_name = request.POST.get('folderName', '')
            project_type = request.POST.get('projectType', '')
            pgsql_user_id = int(request.POST.get('pgsql_user_id', ''))  # 从string变为number
            pgsql_user_name = request.POST.get('pgsql_user_name', '')
            project_id = int(request.POST.get('project_id', '')) # 从string变为number

            print(f"project_id: {project_id}")
            print(f"pgsql_user_id: {pgsql_user_id}")
            print(f"pgsql_user_name: {pgsql_user_name}")
            print(f"project_type: {project_type}")
            print(f"is_folder: {is_folder}")
            print(f"folder_name: {folder_name}")


            if not files:
                return ErrorResponse(msg='没有接收到文件')

            # 准备上传目录
            base_upload_dir = os.path.join(settings.BASE_DIR, 'data_pipeline', 'uploadFiles')

            # 如果目录存在，先清空目录（可选：建议改为用唯一子目录避免冲突）
            if os.path.exists(base_upload_dir):
                try:
                    shutil.rmtree(base_upload_dir)
                except Exception as e:
                    return ErrorResponse(msg=f'清理上传目录失败: {str(e)}')

            os.makedirs(base_upload_dir, exist_ok=True)

            # 如果是文件夹上传，创建带时间戳的子目录
            folder_path = ''
            if is_folder and folder_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                folder_path = f"{folder_name}_{timestamp}"
                upload_dir = os.path.join(base_upload_dir, folder_path)
                os.makedirs(upload_dir, exist_ok=True)
            else:
                upload_dir = os.path.join(base_upload_dir, 'noFolderFileList')
                os.makedirs(upload_dir, exist_ok=True)

            total_files = len(files)
            uploaded_files = []  # 存储上传成功的文件信息

            # === 同步逐个上传文件 ===
            for file in files:
                # 调用保存单个文件的方法（你已实现）, 该方法会把相关文件信息存到 postgresql 数据库
                file_info = self._save_single_file_from_data(
                    {
                        'name': file.name,
                        'content': file.read(),  # 读取内容（注意大文件风险）
                        'size': file.size
                    },
                    upload_dir,
                    is_folder,
                    folder_path,
                    pgsql_user_id,
                    pgsql_user_name,
                    project_id
                )
                if file_info:
                    uploaded_files.append(file_info)
                # 可选：添加小延迟观察
                time.sleep(0.01)

            # === 所有文件上传完成，返回结果 ===
            return SuccessResponse(data={
                'uploaded_files': uploaded_files,
                'total_files': total_files,
                'message': '文件上传成功'
            }, msg='文件上传成功')

        except Exception as e:
            return ErrorResponse(msg=f'文件上传失败：{str(e)}')

    def _save_single_file_from_data(self, file_data, upload_dir, is_folder, folder_path, pgsql_user_id, pgsql_user_name, project_id):
        """
        从文件数据保存单个文件
        """
        try:
            file_name = file_data['name']
            file_content = file_data['content']
            file_size = file_data['size']

            # 文件信息保存 pgsql 数据库
            existing_file = DataPipelineFile.objects.filter(
                project_id=project_id,
                file_name=file_name,
                pgsql_user_id=pgsql_user_id,
                pgsql_user_name=pgsql_user_name
            ).first()

            print(f"_save_single_file_from_data() ------ 已找到对应的数据库数据")

            if existing_file:
                latest_version = DataPipelineFile.objects.filter(
                    project_id=project_id,
                    file_name=file_name,
                    pgsql_user_id=pgsql_user_id,
                    pgsql_user_name=pgsql_user_name
                ).order_by('-version').first().version
                new_version = latest_version + 1
            else:
                new_version = 1

            # 保持原始的文件路径结构（如果是文件夹上传）
            if is_folder:
                # 获取文件的相对路径
                relative_path = file_name
                if '/' in relative_path:
                    # 创建子文件夹
                    sub_folder = os.path.dirname(relative_path)
                    full_sub_folder = os.path.join(upload_dir, sub_folder)
                    os.makedirs(full_sub_folder, exist_ok=True)

                    # 文件名使用最后一部分
                    final_file_name = os.path.basename(relative_path)
                else:
                    final_file_name = relative_path
            else:
                final_file_name = file_name

            # 生成安全的文件名
            safe_filename = f"{final_file_name}"

            # 构建完整的文件路径
            if is_folder and '/' in file_name:
                sub_folder = os.path.dirname(file_name)
                file_path = os.path.join(upload_dir, sub_folder, safe_filename)
            else:
                file_path = os.path.join(upload_dir, safe_filename)

            # 确保目标目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 写入文件
            with open(file_path, 'wb') as destination:
                destination.write(file_content)

            # 文件信息写入 pgsql 数据库
            print(f"project_id: {project_id}")
            print(f"pgsql_user_id: {pgsql_user_id}")
            print(f"pgsql_user_name: {pgsql_user_name}")
            print(f"final_file_name: {final_file_name}")
            print(f"file_size: {file_size}")
            print(f"new_version: {new_version}")

            fileFolder_name=folder_path if is_folder and folder_path else None
            file_record = DataPipelineFile.objects.create(
                project_id=project_id,
                pgsql_user_id=pgsql_user_id,
                pgsql_user_name=pgsql_user_name,
                file_name=final_file_name,
                file_size=file_size,
                version=new_version,
                file_status='正常',
                fileFolder_name=fileFolder_name
            )

            print(f"_save_single_file_from_data() ------ 已创建新的数据库文件记录") 
            print(f"file_record.id: {file_record.id}")

            return {
                'id': file_record.id,
                'name': final_file_name,
                'size': file_size,
                'version': new_version,
                'file_status': '正常',
                'project_id': project_id,
                'pgsql_user_id': pgsql_user_id,
            }

        except Exception as e:
            print(f"保存文件 {file_data['name']} 时出错: {str(e)}")
            return None