#!/usr/bin/env python3
"""
测试文件上传和 SSE 功能
"""

import requests
import json
import time
from io import BytesIO

# 配置
BASE_URL = "http://localhost:8000"
TOKEN = "your_jwt_token_here"  # 需要替换为实际的 JWT token

def test_file_upload():
    """测试文件上传功能"""
    print("开始测试文件上传...")
    
    # 创建测试文件
    test_file_content = b"This is a test file content for EP2002 data pipeline."
    test_file = BytesIO(test_file_content)
    test_file.name = "test_file.bin"
    
    # 准备上传数据
    files = {
        'files': (test_file.name, test_file, 'application/octet-stream')
    }
    
    data = {
        'isFolder': 'false',
        'folderName': '',
        'projectType': 'EP2002'
    }
    
    headers = {
        'Authorization': f'JWT {TOKEN}'
    }
    
    # 发送上传请求
    try:
        response = requests.post(
            f"{BASE_URL}/api/data_pipeline/file/upload/",
            files=files,
            data=data,
            headers=headers
        )
        
        print(f"上传响应状态码: {response.status_code}")
        print(f"上传响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("文件上传成功!")
            return True
        else:
            print("文件上传失败!")
            return False
            
    except Exception as e:
        print(f"上传请求出错: {e}")
        return False

def test_sse_monitoring():
    """测试 SSE 监控功能"""
    print("开始测试 SSE 监控...")
    
    # 准备 SSE 参数
    param = {
        'total_files': 1,
        'is_folder': False,
        'folder_name': '',
        'project_type': 'EP2002',
        'files_info': [
            {
                'name': 'test_file.bin',
                'size': 57,
                'type': 'application/octet-stream'
            }
        ]
    }
    
    # 构建 SSE URL
    sse_url = f"{BASE_URL}/api/data_pipeline/file/sse/upload/?param={requests.utils.quote(json.dumps(param))}"
    
    try:
        # 发送 SSE 请求
        response = requests.get(sse_url, stream=True, timeout=30)
        
        print(f"SSE 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("SSE 连接成功，开始接收数据...")
            
            # 读取 SSE 数据流
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"SSE 数据: {line}")
                    
                    # 解析数据
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])  # 去掉 'data: ' 前缀
                            print(f"解析的数据: {data}")
                            
                            # 如果收到完成消息，退出
                            if data.get('status') == 'completed':
                                print("SSE 监控完成!")
                                break
                                
                        except json.JSONDecodeError:
                            print(f"无法解析 JSON: {line}")
                    
                    elif line.startswith('event: close'):
                        print("SSE 连接关闭")
                        break
            
            return True
        else:
            print("SSE 连接失败!")
            return False
            
    except Exception as e:
        print(f"SSE 请求出错: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("文件上传和 SSE 功能测试")
    print("=" * 50)
    
    # 测试文件上传
    upload_success = test_file_upload()
    
    if upload_success:
        print("\n" + "=" * 30)
        # 等待一下，确保文件已保存
        time.sleep(2)
        
        # 测试 SSE 监控
        sse_success = test_sse_monitoring()
        
        if sse_success:
            print("\n✅ 所有测试通过!")
        else:
            print("\n❌ SSE 测试失败!")
    else:
        print("\n❌ 文件上传测试失败!")

if __name__ == "__main__":
    main()
