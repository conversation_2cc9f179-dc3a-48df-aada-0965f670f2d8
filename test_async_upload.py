#!/usr/bin/env python3
"""
测试新的异步文件上传和 SSE 功能
"""

import requests
import json
import time
import base64
from io import BytesIO

# 配置
BASE_URL = "http://localhost:8000"
TOKEN = "your_jwt_token_here"  # 需要替换为实际的 JWT token

def create_test_files():
    """创建测试文件数据"""
    test_files = []
    
    # 创建几个测试文件
    for i in range(3):
        content = f"This is test file {i+1} content for async upload testing.".encode('utf-8')
        base64_content = base64.b64encode(content).decode('utf-8')
        
        test_files.append({
            'name': f'test_file_{i+1}.txt',
            'size': len(content),
            'type': 'text/plain',
            'content': base64_content
        })
    
    return test_files

def test_async_upload_via_sse():
    """测试通过 SSE 进行异步上传"""
    print("开始测试异步文件上传...")
    
    # 创建测试文件数据
    files_data = create_test_files()
    
    # 准备 SSE 参数
    param = {
        'total_files': len(files_data),
        'is_folder': False,
        'folder_name': '',
        'project_type': 'EP2002',
        'files_info': [
            {
                'name': file_data['name'],
                'size': file_data['size'],
                'type': file_data['type']
            } for file_data in files_data
        ],
        'files_data': files_data,  # 新增：传递文件数据
        'pgsql_user_id': 1,  # 测试用户ID
        'pgsql_user_name': 'test_user',  # 测试用户名
        'project_id': 1  # 测试项目ID
    }
    
    # 构建 SSE URL
    sse_url = f"{BASE_URL}/api/data_pipeline/file/sse/upload/?param={requests.utils.quote(json.dumps(param))}"
    
    print(f"SSE URL: {sse_url}")
    print(f"传递的文件数量: {len(files_data)}")
    
    try:
        # 发送 SSE 请求
        headers = {
            'Authorization': f'JWT {TOKEN}',
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache'
        }
        
        response = requests.get(sse_url, stream=True, timeout=60, headers=headers)
        
        print(f"SSE 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("SSE 连接成功，开始接收异步上传进度...")
            
            uploaded_files = []
            
            # 读取 SSE 数据流
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"SSE 数据: {line}")
                    
                    # 解析数据
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])  # 去掉 'data: ' 前缀
                            print(f"解析的进度数据: {data}")
                            
                            # 显示进度信息
                            if 'progress' in data:
                                print(f"上传进度: {data['progress']}% ({data.get('uploaded_files', 0)}/{data.get('total_files', 0)})")
                            
                            # 如果上传完成
                            if data.get('status') == 'completed':
                                uploaded_files = data.get('files', [])
                                print("异步上传完成!")
                                print(f"上传的文件信息: {uploaded_files}")
                                break
                            
                            # 如果出现错误
                            if data.get('status') == 'error':
                                print(f"上传出错: {data.get('message', 'Unknown error')}")
                                return False
                                
                        except json.JSONDecodeError:
                            print(f"无法解析 JSON: {line}")
                    
                    elif line.startswith('event: close'):
                        print("SSE 连接关闭")
                        break
            
            # 验证结果
            if uploaded_files:
                print(f"\n✅ 异步上传成功! 共上传 {len(uploaded_files)} 个文件")
                for file_info in uploaded_files:
                    print(f"  - {file_info.get('name', 'Unknown')} (ID: {file_info.get('id', 'N/A')})")
                return True
            else:
                print("\n❌ 没有收到上传完成的文件信息")
                return False
            
        else:
            print(f"SSE 连接失败! 状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"SSE 请求出错: {e}")
        return False

def test_file_system_verification():
    """验证文件是否正确保存到文件系统"""
    print("\n开始验证文件系统...")
    
    import os
    upload_dir = "/path/to/backend/data_pipeline/uploadFiles"  # 需要根据实际路径调整
    
    if os.path.exists(upload_dir):
        files = os.listdir(upload_dir)
        print(f"上传目录中的文件: {files}")
        return len(files) > 0
    else:
        print(f"上传目录不存在: {upload_dir}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("异步文件上传和 SSE 功能测试")
    print("=" * 60)
    
    # 测试异步上传
    upload_success = test_async_upload_via_sse()
    
    if upload_success:
        print("\n" + "=" * 40)
        
        # 验证文件系统（可选）
        # fs_success = test_file_system_verification()
        
        print("\n✅ 异步上传测试通过!")
        print("\n测试要点:")
        print("1. ✅ SSE 连接建立成功")
        print("2. ✅ 文件数据通过 SSE 参数传递")
        print("3. ✅ 后端异步处理文件上传")
        print("4. ✅ 实时进度推送正常")
        print("5. ✅ 上传完成后返回文件信息")
        
    else:
        print("\n❌ 异步上传测试失败!")
        print("\n可能的问题:")
        print("1. JWT token 无效或未设置")
        print("2. 后端服务未启动")
        print("3. 数据库连接问题")
        print("4. SSE 接口实现有误")

if __name__ == "__main__":
    print("注意: 请先设置正确的 JWT token 和确保后端服务运行")
    print("TOKEN 变量需要替换为实际的认证令牌")
    print()
    main()
